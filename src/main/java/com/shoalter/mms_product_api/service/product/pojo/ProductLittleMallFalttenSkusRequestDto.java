package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@AllArgsConstructor
@Builder
public class ProductLittleMallFalttenSkusRequestDto implements Serializable {
	private String uuid;

	@JsonProperty("record_row_id")
	@SerializedName("record_row_id")
	private Long recordRowId;
}
