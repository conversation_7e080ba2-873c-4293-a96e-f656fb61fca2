package com.shoalter.mms_product_api.service.product.pojo;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Builder
@Data
public class CheckProductResultDto {
    private boolean result;
    private List<String> errorMessageList;

	public static CheckProductResultDto generate(List<String> errorMessageList) {
		return CheckProductResultDto.builder()
			.errorMessageList(errorMessageList)
			.result(errorMessageList.isEmpty())
			.build();
	}
}
