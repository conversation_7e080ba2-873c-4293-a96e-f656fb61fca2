package com.shoalter.mms_product_api.service.product;

import com.shoalter.mms_product_api.config.type.StorageTemperatureType;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.product.helper.PackingBoxTypeHelper;
import com.shoalter.mms_product_api.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class FindFragileService {

	private final PackingBoxTypeHelper packingBoxTypeHelper;

	public ResponseDto<List<SysParmDo>> start(String buCode, String categoryCode, String storageTemperatureCode) {
		String packingTypeRestrictionCode = packingBoxTypeHelper.generatePackingTypeRestrictionCode(buCode, categoryCode);
//		log.info("packingTypeRestrictionCode = {}", packingTypeRestrictionCode);
		List<String> fragileMenuItemCodeList = generateFragileMenuItemCodeList(packingTypeRestrictionCode, storageTemperatureCode);
		List<SysParmDo> fragileMenuItemList = generateFragileMenuItemList(fragileMenuItemCodeList);
		return ResponseDto.<List<SysParmDo>>builder().data(fragileMenuItemList).status(1).build();
	}

	private List<String> generateFragileMenuItemCodeList(String packingTypeRestrictionCode, String storageTemperatureCode) {
		if(StringUtil.isEmpty(packingTypeRestrictionCode)) {
			if (StorageTemperatureType.FROZEN.equals(storageTemperatureCode)) {
				return List.of("N");
			}
			return List.of("Y", "N");
		} else {
			switch (storageTemperatureCode) {
				case StorageTemperatureType.FROZEN:
					switch (packingTypeRestrictionCode) {
						case "E":
						case "T":
							return List.of("N");
					}
					break;
				case StorageTemperatureType.CHILLED:
					switch (packingTypeRestrictionCode) {
						case "M":
							return List.of("Y");
						case "J":
						case "N":
						case "K":
						case "U":
							return List.of("N");
					}
					break;
				case StorageTemperatureType.AIRCON:
					switch (packingTypeRestrictionCode) {
						case "O":
						case "V":
							return List.of("Y");
						case "P":
						case "S":
							return List.of("N");
					}
					break;
				case StorageTemperatureType.ROOM_TEMPERATURE:
					switch (packingTypeRestrictionCode) {
						case "G":
						case "F":
						case "Y":
							return List.of("Y");
						case "P":
						case "S":
						case "X":
							return List.of("N");
					}
					break;
			}
		}
		return List.of();
	}

	private List<SysParmDo> generateFragileMenuItemList(List<String> fragileMenuItemCodeList) {
		List<SysParmDo> list = new ArrayList<>();
		for (String code: fragileMenuItemCodeList) {
			SysParmDo sysParmDo = new SysParmDo();
			sysParmDo.setCode(code);
			sysParmDo.setParmValue(String.valueOf("Y".equalsIgnoreCase(code)));
			list.add(sysParmDo);
		}
		return list;
	}
}
