package com.shoalter.mms_product_api.service.product.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.io.Serializable;

@Data
public class ProductMasterBusinessUnitStatusDto implements Serializable {

    @JsonProperty("hktv")
    private String hktv;

    @JsonProperty("3pl")
	@SerializedName("3pl")
    private String thirdPartyLogistics;

    @JsonProperty("little_mall")
	@SerializedName("little_mall")
    private String littleMall;
}
