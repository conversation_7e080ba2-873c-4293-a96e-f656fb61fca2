package com.shoalter.mms_product_api.service.bundle.pojo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import com.shoalter.mms_product_api.service.product.pojo.LittleMallProductDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterIIDSBuDto;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class BuBundleDto {

    // multiple bu in additional
    private HktvBundleDto hktv;

	@JsonProperty("little_mall")
	@SerializedName("little_mall")
	private LittleMallProductDto littleMall;
	private ProductMasterIIDSBuDto iids;
}
