package com.shoalter.mms_product_api.service.mms_product.pojo;

import com.shoalter.mms_product_api.dao.repository.api.pojo.SaveProductRecordDo;
import com.shoalter.mms_product_api.service.product.pojo.SingleEditProductDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchProductAsyncDto {

	private List<SingleEditProductDto> productList = new ArrayList<>();
	private SaveProductRecordDo saveProductRecordDo;

}
