package com.shoalter.mms_product_api.mapper;

import com.shoalter.mms_product_api.config.product.OnlineStatusEnum;
import com.shoalter.mms_product_api.config.product.ProductFileConfig;
import com.shoalter.mms_product_api.config.type.ConstantType;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiBatchEditPriceMainRequestData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiBatchEditProductReadyDaysMainRequestData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiProductInfoImageResponseData;
import com.shoalter.mms_product_api.service.openApi.pojo.OapiProductInfoMainResponseData;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditPriceDto;
import com.shoalter.mms_product_api.service.product.pojo.BatchEditProductReadyDaysDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductBarcodeDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterResultDto;
import com.shoalter.mms_product_api.util.CollectionUtil;
import com.shoalter.mms_product_api.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(
	componentModel = MappingConstants.ComponentModel.SPRING,
	builder = @Builder)
public interface OapiDataMapper {

	OapiDataMapper INSTANCE = Mappers.getMapper(OapiDataMapper.class);

	@Mapping(target = "discountTextSc", source = "discountTextZhCN")
	BatchEditPriceDto toOapiBatchEditPriceMainRequestData(OapiBatchEditPriceMainRequestData data);

	BatchEditProductReadyDaysDto toBatchEditProductReadyDaysDto(OapiBatchEditProductReadyDaysMainRequestData data);

	@Mapping(target = "createdBy", source = "mmsCreateUser")
	@Mapping(target = "createdDate", source = "mmsCreateTime")
	@Mapping(target = "lastUpdatedBy", source = "mmsModifiedUser")
	@Mapping(target = "lastUpdatedDate", source = "mmsModifiedTime")
	@Mapping(target = "productCode", source = "productId")
	@Mapping(target = "skuName", source = "skuNameEn")
	@Mapping(target = "skuNameTchi", source = "skuNameCh")
	@Mapping(target = "skuNameZhCN", source = "skuNameSc")
	@Mapping(target = "primaryCategoryCode", source = "additional.hktv.primaryCategoryCode")
	@Mapping(target = "skuSDescEn", source = "additional.hktv.skuShortDescriptionEn")
	@Mapping(target = "skuSDescCh", source = "additional.hktv.skuShortDescriptionCh")
	@Mapping(target = "skuSDescZhCN", source = "additional.hktv.skuShortDescriptionSc")
	@Mapping(target = "skuLDescEn", source = "additional.hktv.skuLongDescriptionEn")
	@Mapping(target = "skuLDescCh", source = "additional.hktv.skuLongDescriptionCh")
	@Mapping(target = "skuLDescZhCN", source = "additional.hktv.skuLongDescriptionSc")
	@Mapping(target = "invoiceRemarksEn", source = "additional.hktv.invoiceRemarksEn")
	@Mapping(target = "invoiceRemarksCh", source = "additional.hktv.invoiceRemarksCh")
	@Mapping(target = "invoiceRemarksZhCN", source = "additional.hktv.invoiceRemarksSc")
	@Mapping(target = "videoLinkEn", source = "additional.hktv.videoLinkTextEn")
	@Mapping(target = "videoLinkCh", source = "additional.hktv.videoLinkTextCh")
	@Mapping(target = "videoLinkZhCN", source = "additional.hktv.videoLinkTextSc")
	@Mapping(target = "barcode", source = "barcodes", qualifiedByName = "toBarcode")
	@Mapping(target = "manuCountry", source = "manufacturedCountry")
	@Mapping(target = "packHeight", source = "packingHeight")
	@Mapping(target = "packLength", source = "packingLength")
	@Mapping(target = "packDepth", source = "packingDepth")
	@Mapping(target = "packDimensionUnit", source = "packingDimensionUnit")
	@Mapping(target = "packBoxType", source = "packingBoxType")
	@Mapping(target = "packSpecEn", source = "additional.hktv.packingSpecEn")
	@Mapping(target = "packSpecCh", source = "additional.hktv.packingSpecCh")
	@Mapping(target = "packSpecZhCN", source = "additional.hktv.packingSpecSc")
	@Mapping(target = "currencyCode", source = "additional.hktv.currency")
	@Mapping(target = "sellingPrice", source = "additional.hktv.sellingPrice")
	@Mapping(target = "mallDollar", source = "additional.hktv.mallDollar")
	@Mapping(target = "mallDollarVip", source = "additional.hktv.vipMallDollar")
	@Mapping(target = "productReadyMethod", source = "additional.hktv.productReadyMethod")
	@Mapping(target = "deliveryMethod", source = "additional.hktv.deliveryMethod")
	@Mapping(target = "returnDays", source = "additional.hktv.returnDays")
	@Mapping(target = "productReadyDays", source = "additional.hktv.productReadyDays")
	@Mapping(target = "pickupDays", source = "additional.hktv.pickupDays")
	@Mapping(target = "pickupTimeslot", source = "additional.hktv.pickupTimeslot")
	@Mapping(target = "invisibleFlag", source = "additional.hktv.visibility", qualifiedByName = "toggleYN")
	@Mapping(target = "featureStartTime", source = "additional.hktv.featureStartTime", dateFormat = "yyyy-MM-dd'T'HH:mm:ss")
	@Mapping(target = "featureEndTime", source = "additional.hktv.featureEndTime", dateFormat = "yyyy-MM-dd'T'HH:mm:ss")
	@Mapping(target = "voucherType", source = "additional.hktv.voucherType")
	@Mapping(target = "voucherDisplayType", source = "additional.hktv.voucherDisplayType")
	@Mapping(target = "userMax", source = "additional.hktv.userMax")
	@Mapping(target = "redeemStartDate", source = "additional.hktv.redeemStartDate", dateFormat = "yyyy-MM-dd'T'HH:mm:ss")
	@Mapping(target = "urgent", source = "additional.hktv.urgent")
	@Mapping(target = "expiryType", source = "additional.hktv.expiryType")
	@Mapping(target = "fixedRedemptionDate", source = "additional.hktv.fixedRedemptionDate", dateFormat = "yyyy-MM-dd'T'HH:mm:ss")
	@Mapping(target = "uponPurchaseDate", source = "additional.hktv.uponPurchaseDate")
	@Mapping(target = "finePrintEn", source = "additional.hktv.finePrintEn")
	@Mapping(target = "finePrintCh", source = "additional.hktv.finePrintCh")
	@Mapping(target = "finePrintZhCN", source = "additional.hktv.finePrintSc")
	@Mapping(target = "cost", source = "additional.hktv.cost")
	@Mapping(target = "removalServices", source = "additional.hktv.needRemovalServices")
	@Mapping(target = "field1", source = "option1")
	@Mapping(target = "value1", source = "option1Value")
	@Mapping(target = "field2", source = "option2")
	@Mapping(target = "value2", source = "option2Value")
	@Mapping(target = "field3", source = "option3")
	@Mapping(target = "value3", source = "option3Value")
	@Mapping(target = "discountText", source = "additional.hktv.discountTextEn")
	@Mapping(target = "discountTextTchi", source = "additional.hktv.discountTextCh")
	@Mapping(target = "discountTextZhCN", source = "additional.hktv.discountTextSc")
	@Mapping(target = "style", source = "additional.hktv.style")
	@Mapping(target = "goodsType", source = "additional.hktv.goodsType")
	@Mapping(target = "warrantyPeriodUnit", source = "additional.hktv.warrantyPeriodUnit")
	@Mapping(target = "warrantyPeriod", source = "additional.hktv.warrantyPeriod")
	@Mapping(target = "warrantySupplierEn", source = "additional.hktv.warrantySupplierEn")
	@Mapping(target = "warrantySupplierCh", source = "additional.hktv.warrantySupplierCh")
	@Mapping(target = "warrantySupplierZhCN", source = "additional.hktv.warrantySupplierSc")
	@Mapping(target = "serviceCentreAddressEn", source = "additional.hktv.serviceCentreAddressEn")
	@Mapping(target = "serviceCentreAddressCh", source = "additional.hktv.serviceCentreAddressCh")
	@Mapping(target = "serviceCentreAddressZhCN", source = "additional.hktv.serviceCentreAddressSc")
	@Mapping(target = "serviceCentreEmail", source = "additional.hktv.serviceCentreEmail")
	@Mapping(target = "serviceCentreContact", source = "additional.hktv.serviceCentreContact")
	@Mapping(target = "warrantyRemarkEn", source = "additional.hktv.warrantyRemarkEn")
	@Mapping(target = "warrantyRemarkCh", source = "additional.hktv.warrantyRemarkCh")
	@Mapping(target = "warrantyRemarkZhCN", source = "additional.hktv.warrantyRemarkSc")
	@Mapping(target = "onOfflineStatus", source = "additional.hktv.onlineStatus", qualifiedByName = "toOnOfflineStatus")
	@Mapping(target = "commissionRate", source = "additional.hktv.commissionRate")
	@Mapping(target = "isPrimarySku", source = "additional.hktv.isPrimarySku")
	@Mapping(target = "imagesMainPhotoList", source = "additional.hktv.mainPhoto", qualifiedByName = "toMainPhotoList")
	@Mapping(target = "imagesProductPhotoList", source = "additional.hktv.variantProductPhoto", qualifiedByName = "toVariantProductPhotoList")
	@Mapping(target = "imagesOtherPhotoList", source = "additional.hktv.variantProductPhoto", qualifiedByName = "toOtherPhotoList")
	@Mapping(target = "imagesAdvertisingList", source = "additional.hktv.advertisingPhoto", qualifiedByName = "toAdvertisingPhotoList")
	@Mapping(target = "partnerInfo", source = "additional.hktv.partnerInfo")
	@Mapping(target = "externalPlatform", source = "additional.hktv.externalPlatform")
	OapiProductInfoMainResponseData toOapiProductInfoMainResponseData(ProductMasterResultDto source);


	@Named("toBarcode")
	default String toBarcode(List<ProductBarcodeDto> barcodes) {
		if (CollectionUtil.isNotEmpty(barcodes)) {
			return barcodes.get(0).getEan();
		}
		return null;
	}

	@Named("toOnOfflineStatus")
	default String toOnOfflineStatus(OnlineStatusEnum onlineStatusEnum) {
		if (onlineStatusEnum == null) {
			return null;
		}
		return onlineStatusEnum.name();
	}

	@Named("toggleYN")
	default String toggleYN(String visibility) {
		if (!StringUtils.equalsAnyIgnoreCase(visibility, ConstantType.CONSTANT_YES, ConstantType.CONSTANT_NO)) {
			return null;
		}
		return StringUtil.toggleYN(visibility);
	}

	@Named("toMainPhotoList")
	default List<OapiProductInfoImageResponseData> toMainPhotoList(String photo) {
		OapiProductInfoImageResponseData data = toImageData(ProductFileConfig.IMAGE_TYPE_MAIN, photo);
		return data == null ? List.of() : List.of(data);
	}

	@Named("toAdvertisingPhotoList")
	default List<OapiProductInfoImageResponseData> toAdvertisingPhotoList(String photo) {
		OapiProductInfoImageResponseData data = toImageData(ProductFileConfig.IMAGE_TYPE_ADVERTISING, photo);
		return data == null ? null : List.of(data);
	}

	@Named("toOtherPhotoList")
	default List<OapiProductInfoImageResponseData> toOtherPhotoList(List<String> photo) {
		if (photo == null) {
			return null;
		}
		return photo.stream().map(p -> toImageData(ProductFileConfig.IMAGE_TYPE_OTHER_B, p)).collect(Collectors.toList());
	}

	@Named("toVariantProductPhotoList")
	default List<OapiProductInfoImageResponseData> toVariantProductPhotoList(List<String> photo) {
		if (photo == null) {
			return null;
		}
		return photo.stream().map(p -> toImageData(ProductFileConfig.IMAGE_TYPE_OTHER_A, p)).collect(Collectors.toList());
	}

	default OapiProductInfoImageResponseData toImageData(String imageType, String photo) {
		if (StringUtil.isEmpty(photo)) {
			return null;
		}

		OapiProductInfoImageResponseData oapiProductInfoImageResponseData = new OapiProductInfoImageResponseData();
		oapiProductInfoImageResponseData.setFileName(photo.substring(photo.lastIndexOf("/") + 1));
		oapiProductInfoImageResponseData.setFilePath(photo);
		oapiProductInfoImageResponseData.setImageType(ProductFileConfig.IMAGE_TYPE_MAIN);

		return oapiProductInfoImageResponseData;
	}
}
