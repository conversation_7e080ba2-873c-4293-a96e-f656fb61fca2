package com.shoalter.mms_product_api.helper;

import com.shoalter.mms_product_api.config.product.SysParmSegmentEnum;
import com.shoalter.mms_product_api.dao.repository.system.pojo.SysParmDo;
import com.shoalter.mms_product_api.service.product.helper.SysParamHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class CacheHelper {

	private final SysParamHelper systemParamRepository;

	@Transactional(readOnly = true)
	@Cacheable(cacheManager = "systemParamCacheManger", cacheNames = "systemParamList", key = "'name_'+ #sysParmSegmentEnum")
	public List<SysParmDo> getSystemParamsBySegment(SysParmSegmentEnum sysParmSegmentEnum) {
		return systemParamRepository.getSystemParamsBySegment(sysParmSegmentEnum);
	}

	@Transactional(readOnly = true)
	@Cacheable(cacheManager = "salesChannelCacheManger", cacheNames = "salesChannelList", key = "'name_'+ #sysParmSegmentEnums")
	public List<SysParmDo> getSalesChannelSystemParamsBySegment(List<SysParmSegmentEnum> sysParmSegmentEnums) {
		return systemParamRepository.getSystemParamsBySegments(sysParmSegmentEnums);
	}

	@Scheduled(fixedRate = 1000 * 60 * 5)
	@CacheEvict(cacheManager = "systemParamCacheManger", cacheNames = {"systemParamList"}, allEntries = true)
	public void emptySysParamCache() {
		log.info("Empty System Param Cache");
	}

	@Scheduled(fixedRate = 1000 * 30)
	@CacheEvict(cacheManager = "salesChannelCacheManger", cacheNames = {"salesChannelList"}, allEntries = true)
	public void emptySalesChannelCache() {
		log.info("Empty Sales Channel Cache");
	}
}
