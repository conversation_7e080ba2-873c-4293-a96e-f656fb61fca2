package com.shoalter.mms_product_api.dao.repository.approval_deal;

import com.shoalter.mms_product_api.dao.repository.approval_deal.pojo.CommissionApprovalDealDo;
import com.shoalter.mms_product_api.service.approval_deal.pojo.CommissionApprovalDealDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Repository
public interface CommissionApprovalDealRepository extends JpaRepository<CommissionApprovalDealDo, Long> {

	@Query(value = "SELECT cad.PRIMARY_CATEGORY_CODE primaryCategoryCode, " +
		"b.BRAND_NAME_EN brandName, cpt.COMMISSION_RATE commissionRate, tcs.TIER_ONE_THRESHOLD tier1Threshold, " +
		"tcs.TIER_ONE_COMMISSION_RATE tier1CommissionRate, tcs.TIER_TWO_THRESHOLD tier2Threshold, " +
		"tcs.TIER_TWO_COMMISSION_RATE tier2CommissionRate, cad.PACKING_BOX_TYPE packingBoxType, ad.APPROVAl_STATUS approvalStatus, " +
		"cad.PRODUCT_TYPE_CODE productTypeCode " +
		"FROM COMMISSION_APPROVAL_DEAL cad " +
		"JOIN APPROVAL_DEAL ad ON cad.ID = ad.ID " +
		"JOIN CONTRACT_PROD_TERMS cpt ON cad.CONTRACT_PROD_TERMS_ID = cpt.id " +
		"LEFT JOIN THRESHOLD_COMMISSION_SETTING tcs on cpt.id = tcs.CONTRACT_PROD_TERMS_ID " +
		"JOIN BUS_UNIT bu ON ad.BU = bu.CODE " +
		"LEFT JOIN BU_PRODUCT_CATEGORY bpc ON bu.ID = bpc.Bus_Unit_id AND cad.PRIMARY_CATEGORY_CODE = bpc.Product_cat_code AND bpc.Product_cat_type = 'STORE' " +
		"LEFT JOIN BRAND b ON cad.BRAND_ID = b.ID " +
		"WHERE ad.BU = :bu AND ad.STOREFRONT_STORE_CODE = :storefrontStoreCode " +
		"AND (COALESCE(:approvalStatus, NULL) IS NULL OR ad.APPROVAl_STATUS IN (:approvalStatus)) " +
		"AND ad.SKU_CODE = :skuCode ", nativeQuery = true)
	List<CommissionApprovalDealDto> findCommissionApprovalDealDto(String bu, String storefrontStoreCode, List<String> approvalStatus, String skuCode);

	@Query(value = "SELECT * FROM COMMISSION_APPROVAL_DEAL cad " +
		"JOIN APPROVAL_DEAL ad ON cad.ID = ad.ID " +
		"WHERE ad.BU = :bu AND ad.STOREFRONT_STORE_CODE = :storefrontStoreCode AND ad.APPROVAl_STATUS IN :approvalStatus AND ad.SKU_CODE = :skuCode "
		, nativeQuery = true)
	List<CommissionApprovalDealDo> findByBuAndStorefrontStoreCodeAndSkuCodeAndApprovalStatusIn(String bu, String storefrontStoreCode, List<String> approvalStatus, String skuCode);

	@Query(value = "SELECT * FROM COMMISSION_APPROVAL_DEAL cad " +
		"JOIN APPROVAL_DEAL ad ON cad.ID = ad.ID " +
		"WHERE ad.BU = :bu " +
		"AND (COALESCE(:storefrontStoreCodes, NULL) IS NULL OR ad.STOREFRONT_STORE_CODE IN (:storefrontStoreCodes)) " +
		"AND ad.CREATED_DATE BETWEEN :startDate AND :endDate " + ApprovalDealRepository.permissionRmSql +
		"ORDER BY ad.LAST_UPDATED_DATE DESC ", nativeQuery = true)
	List<CommissionApprovalDealDo> findByBuAndStoreAndUpdatedDate(UserDto userDto, String bu, List<String> storefrontStoreCodes, Date startDate, Date endDate);

	List<CommissionApprovalDealDo> findByCreatedDateBetween(LocalDateTime start, LocalDateTime end);
}




