package com.shoalter.mms_product_api.dao.repository.product.pojo;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "PRODUCT_BARCODE")
@Data
public class ProductBarcodeDo {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "id")
    private Integer id;
    @Column(name = "PRODUCT_ID")
    private Integer productId;
    @Column(name = "SEQUENCE_NO")
    private Integer sequenceNo;
    @Column(name = "BARCODE")
    private String barcode;
    @Column(name = "CREATED_BY")
    private String createdBy;
    @Column(name = "CREATED_DATE")
    private Date createdDate;
    @Column(name = "LAST_UPDATED_BY")
    private String lastUpdatedBy;
    @Column(name = "LAST_UPDATED_DATE")
    private Date lastUpdatedDate;
}
