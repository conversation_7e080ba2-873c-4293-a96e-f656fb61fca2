package com.shoalter.mms_product_api.config.product.edit_column;

import com.shoalter.mms_product_api.config.product.SaveProductType;
import com.shoalter.mms_product_api.exception.SystemException;
import com.shoalter.mms_product_api.service.product.template.IProductTemplateHelper;
import com.shoalter.mms_product_api.service.product.template.ProductTemplateAllColumnHelper;
import com.shoalter.mms_product_api.service.product.template.ProductTemplateExtendedWarrantyHelper;
import com.shoalter.mms_product_api.service.product.template.ProductTemplateForceOfflineHelper;
import com.shoalter.mms_product_api.service.product.template.ProductTemplateHktvProductTranslateHelper;
import com.shoalter.mms_product_api.service.product.template.ProductTemplateOnlineStatusHelper;
import com.shoalter.mms_product_api.service.product.template.ProductTemplateOverseaReserveRegionHelper;
import com.shoalter.mms_product_api.service.product.template.ProductTemplatePackingDimensionHelper;
import com.shoalter.mms_product_api.service.product.template.ProductTemplateSkuPriceHelper;
import com.shoalter.mms_product_api.service.product.template.ProductTemplateVisibilityHelper;
import com.shoalter.mms_product_api.service.product.template.TemplateInterface;
import com.shoalter.mms_product_api.util.SpringBeanProvider;
import lombok.AllArgsConstructor;
import lombok.Getter;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Set;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum TemplateTypeEnum {
	ALL_COLUMN("All Column", AllColumnTemplateColumnEnum.class),
	PACKING_DIMENSION("Packing Dimension", PackingDimensionTemplateColumnEnum.class),
	SKU_PRICE("SKU Price", SkuPriceTemplateColumnEnum.class),
	ONLINE_STATUS("Online/Offline", OnlineStatusTemplateColumnEnum.class),
	VISIBILITY("Visibility", VisibilityTemplateColumnEnum.class),
	EXTENDED_WARRANTY("Extended Warranty", ExtendedWarrantyTemplateColumnEnum.class),
	OVERSEA_RESERVE_REGION("Oversea Reserve Region", OverseaReserveRegionTemplateColumnEnum.class),
	HKTV_PRODUCT_TRANSLATE("HKTV Product Translate", HktvProductTranslateTemplateColumnEnum.class),
	ALL_COLUMN_OPEN_API("All Column", AllColumnTemplateColumnEnum.class),
	FORCE_OFFLINE("Product_Suspended", ForceOfflineTemplateColumnEnum.class);


	private final String exportName;
	private final Class<? extends TemplateInterface<?>> templateColumnEnum;

	public static IProductTemplateHelper<? extends TemplateInterface<?> > getService(TemplateTypeEnum type) {
		switch (type) {
			case ALL_COLUMN:
			case ALL_COLUMN_OPEN_API:
				return SpringBeanProvider.getBean(ProductTemplateAllColumnHelper.class);
			case VISIBILITY:
				return SpringBeanProvider.getBean(ProductTemplateVisibilityHelper.class);
			case PACKING_DIMENSION:
				return SpringBeanProvider.getBean(ProductTemplatePackingDimensionHelper.class);
			case ONLINE_STATUS:
				return SpringBeanProvider.getBean(ProductTemplateOnlineStatusHelper.class);
			case SKU_PRICE:
				return SpringBeanProvider.getBean(ProductTemplateSkuPriceHelper.class);
			case EXTENDED_WARRANTY:
				return SpringBeanProvider.getBean(ProductTemplateExtendedWarrantyHelper.class);
			case OVERSEA_RESERVE_REGION:
				return SpringBeanProvider.getBean(ProductTemplateOverseaReserveRegionHelper.class);
			case HKTV_PRODUCT_TRANSLATE:
				return SpringBeanProvider.getBean(ProductTemplateHktvProductTranslateHelper.class);
			case FORCE_OFFLINE:
				return SpringBeanProvider.getBean(ProductTemplateForceOfflineHelper.class);
			default:
				throw new IllegalArgumentException("Invalid service type: " + type);
		}
	}

	public static final Set<TemplateTypeEnum> ALL_COLUMN_ENUM_SET = Set.of(ALL_COLUMN, ALL_COLUMN_OPEN_API);

	public static TemplateTypeEnum findTemplateTypeBySaveProductType(int saveType) {
		switch (saveType) {
			case SaveProductType.BATCH_EDIT_PRODUCT_PACKAGING_DIMENSION:
				return PACKING_DIMENSION;
			case SaveProductType.BATCH_EDIT_PRODUCT_PRICE:
			case SaveProductType.BATCH_EDIT_PRICE_EXCHANGE_RATE_RMB:
				return SKU_PRICE;
			case SaveProductType.BATCH_EDIT_PRODUCT_ONLINE_STATUS:
				return ONLINE_STATUS;
			case SaveProductType.BATCH_EDIT_PRODUCT_VISIBILITY:
				return VISIBILITY;
			case SaveProductType.BATCH_CRATE_BINDING_EXTENDED_WARRANTY:
			case SaveProductType.BATCH_DELETE_BINDING_EXTENDED_WARRANTY:
				return EXTENDED_WARRANTY;
			case SaveProductType.BATCH_EDIT_PRODUCT_OVERSEA_RESERVE_REGION:
				return OVERSEA_RESERVE_REGION;
			case SaveProductType.BATCH_EDIT_HKTV_PRODUCT_TRANSLATE:
				return HKTV_PRODUCT_TRANSLATE;
			case SaveProductType.SINGLE_EDIT_PRODUCT_FORCE_OFFLINE:
			case SaveProductType.BATCH_EDIT_PRODUCT_FORCE_OFFLINE:
				return FORCE_OFFLINE;
			default:
				throw new IllegalArgumentException("Invalid saveType: " + saveType);
		}
	}
}

@Converter(autoApply = true)
class TemplateTypeEnumConverter implements AttributeConverter<TemplateTypeEnum, String>
{
	@Override
	public String convertToDatabaseColumn(TemplateTypeEnum attribute) {
		if (attribute == null) {
			return "";
		}
		return attribute.name();
	}

	@Override
	public TemplateTypeEnum convertToEntityAttribute(String dbData) {
		if (dbData == null || dbData.isEmpty()) {
			return null;
		}
		return Stream.of(TemplateTypeEnum.values()).filter(c -> c.name().equals(dbData)).findFirst()
			.orElseThrow(IllegalArgumentException::new);
	}
}
