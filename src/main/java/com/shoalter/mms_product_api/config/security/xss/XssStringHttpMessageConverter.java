package com.shoalter.mms_product_api.config.security.xss;

import org.springframework.http.HttpInputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

public class XssStringHttpMessageConverter extends StringHttpMessageConverter {

    private static final MediaType APPLICATION_PLUS_JSON = new MediaType("application", "*+json");

    @Override
    protected String readInternal(Class<? extends String> clazz, HttpInputMessage inputMessage) throws IOException {
        Charset charset = getCharsetWith(inputMessage.getHeaders().getContentType());
        String input = StreamUtils.copyToString(inputMessage.getBody(), charset);

        return XssUtils.escapeHtml(input);
    }

    private Charset getCharsetWith(@Nullable MediaType contentType) {
        if (contentType != null) {
            Charset charset = contentType.getCharset();
            if (charset != null) {
                return charset;
            }
            else if (contentType.isCompatibleWith(MediaType.APPLICATION_JSON) ||
                    contentType.isCompatibleWith(APPLICATION_PLUS_JSON)) {
                // Matching to AbstractJackson2HttpMessageConverter#DEFAULT_CHARSET
                return StandardCharsets.UTF_8;
            }
        }
        Charset charset = getDefaultCharset();
        Assert.state(charset != null, "No default charset");
        return charset;
    }
}
