package com.shoalter.mms_product_api.helper;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.Arguments;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.stream.Stream;

class TranslationHelperTest {

	private TranslationHelper translationHelper;

	@BeforeEach
	public void setUp() {
		translationHelper = new TranslationHelper();
	}

	private static Stream<Arguments> provideTestCases() {
		return Stream.of(
			Arguments.of(
				"雪櫃中都是牛油, 我的硬碟已經滿了.",
				"冰箱中都是黄油, 我的硬盘已经满了.",
				"Basic translation with custom dictionary"
			),
			Arguments.of(
				"IRIS OHYAMA - [全新第4代] (新版除塵蟎吸塵機) 雪櫃和冷氣機都在客廳裡。桌上有一些膠袋，還有一個抽濕機。雪櫃和冷氣機都在客廳裡。桌上有一些膠袋，還有一個抽濕機。\t\n" +
					"HKTVmall  雪櫃和冷氣機都在客廳裡。\t\n桌上有一些膠袋，還有一個抽濕機。雪櫃和冷氣機都在客廳裡。桌上有一些膠袋，還有一個抽濕機。雪櫃和冷氣機都在客廳裡。桌上有一些膠袋，還有一個抽濕機。 | 冷凍0-4°C | 香港製造 Organic Energy Ball/Ladoo 5pcs pack",
				"IRIS OHYAMA - [全新第4代] (新版除尘螨吸尘机) 冰箱和空调都在客厅里。桌上有一些塑料袋，还有一个除湿机。冰箱和空调都在客厅里。桌上有一些塑料袋，还有一个除湿机。\t\n" +
					"HKTVmall  冰箱和空调都在客厅里。\t\n桌上有一些塑料袋，还有一个除湿机。冰箱和空调都在客厅里。桌上有一些塑料袋，还有一个除湿机。冰箱和空调都在客厅里。桌上有一些塑料袋，还有一个除湿机。 | 冷冻0-4°C | 香港制造 Organic Energy Ball/Ladoo 5pcs pack",
				"Large text translation"
			),
			Arguments.of(
				"這裡有雪櫃、生果和一台冷氣機。",
				"这里有冰箱、水果和一台空调。",
				"Mixed content translation"
			),
			Arguments.of(
				"這是一段沒有自定義字典匹配的文字。",
				"这是一段没有自定义字典匹配的文本。",
				"Text without custom dictionary matches"
			),
			Arguments.of(
				"冷氣機和冷氣機都很貴。",
				"空调和空调都很贵。",
				"Text with repeating words"
			),
			Arguments.of(
				"雪櫃😊和冷氣機😂都在客廳裡。",
				"冰箱😊和空调😂都在客厅里。",
				"Text with emojis"
			),
			Arguments.of(
				"I love my 雪櫃😊 and 冷氣機😂!",
				"I love my 冰箱😊 and 空调😂!",
				"Mixed language with emojis"
			)
		);
	}

	@ParameterizedTest
	@MethodSource("provideTestCases")
	void testTranslateIfNotEmpty(String input, String expected, String testDescription) {
		String result = translationHelper.translateIfNotEmpty(input);
		assertEquals(expected, result, testDescription);
	}
}
