package com.shoalter.mms_product_api.service.openApi.helper;

import com.shoalter.mms_product_api.config.product.StatusCodeEnum;
import com.shoalter.mms_product_api.dao.repository.store.StoreRepository;
import com.shoalter.mms_product_api.dao.repository.store.pojo.StoreContractMerchantDo;
import com.shoalter.mms_product_api.service.base.pojo.ResponseDto;
import com.shoalter.mms_product_api.service.product.IsWhiteListMerchantService;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.MessageSource;

import java.util.Collections;
import java.util.List;

@ExtendWith(MockitoExtension.class)
public class OapiHelperTest {

	@InjectMocks
	private OapiHelper oapiHelper;

	@Mock
	private StoreRepository storeRepository;

	@Mock
	private MessageSource messageSource;

	@Mock
	private IsWhiteListMerchantService isWhiteListMerchantService;

	private final static String TEST_STRING = "test";

	@Test
	public void validateStore_validate_success() {
		//Given
		Mockito.when(storeRepository.findStoreContractMerchantByStorefrontStoreCode(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
			.thenReturn(List.of(getStoreContractMerchantDo()));
		Mockito.when(isWhiteListMerchantService.start(Mockito.anyInt()))
			.thenReturn(ResponseDto.success(true));

		//When
		ResponseDto<StoreContractMerchantDo> result = oapiHelper.validateStore(TEST_STRING, TEST_STRING, TEST_STRING);

		//Then
		Assertions.assertThat(result.getStatus()).isEqualTo(StatusCodeEnum.SUCCESS.getCode());
	}

	@Test
	public void validateStore_storeNotFound_returnErrorMessage() {
		//Given
		Mockito.when(storeRepository.findStoreContractMerchantByStorefrontStoreCode(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
			.thenReturn(Collections.emptyList());
		Mockito.when(messageSource.getMessage(Mockito.anyString(), Mockito.any(), Mockito.any()))
			.thenReturn(TEST_STRING);

		//When
		ResponseDto<StoreContractMerchantDo> result = oapiHelper.validateStore(TEST_STRING, TEST_STRING, TEST_STRING);

		//Then
		Assertions.assertThat(result.getStatus()).isEqualTo(StatusCodeEnum.FAIL.getCode());
		Assertions.assertThat(result.getErrorMessageList()).isNotEmpty();
	}

	@Test
	public void validateStore_storeNotInWhiteList_returnErrorMessage() {
		//Given
		Mockito.when(storeRepository.findStoreContractMerchantByStorefrontStoreCode(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
			.thenReturn(List.of(getStoreContractMerchantDo()));
		Mockito.when(isWhiteListMerchantService.start(Mockito.anyInt()))
			.thenReturn(ResponseDto.success(false));
		Mockito.when(messageSource.getMessage(Mockito.anyString(), Mockito.any(), Mockito.any()))
			.thenReturn(TEST_STRING);

		//When
		ResponseDto<StoreContractMerchantDo> result = oapiHelper.validateStore(TEST_STRING, TEST_STRING, TEST_STRING);

		//Then
		Assertions.assertThat(result.getStatus()).isEqualTo(StatusCodeEnum.FAIL.getCode());
		Assertions.assertThat(result.getErrorMessageList()).isNotEmpty();
	}

	private StoreContractMerchantDo getStoreContractMerchantDo() {
		return new StoreContractMerchantDo() {
			@Override
			public Integer getMerchantId() {
				return 0;
			}

			@Override
			public String getMerchantName() {
				return "";
			}

			@Override
			public Integer getStoreId() {
				return 0;
			}

			@Override
			public String getStoreCode() {
				return "";
			}

			@Override
			public String getStorefrontCode() {
				return "";
			}

			@Override
			public Integer getContractId() {
				return 0;
			}

			@Override
			public String getContractType() {
				return "";
			}
		};
	}
}
