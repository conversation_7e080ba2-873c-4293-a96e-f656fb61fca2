package com.shoalter.mms_product_api.service.product;

import static org.junit.jupiter.api.Assertions.assertTimeoutPreemptively;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.ProductMasterStatusEnum;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductHelper;
import com.shoalter.mms_product_api.service.product.pojo.EditInvisibleProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.EditInvisibleRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterBaseResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.request.ProductMasterUpdateVisibilityRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterSearchVisibilityResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterUpdateVisibilityResponseDto;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class BatchEditInvisibleFlagServiceTest {

    private BatchEditInvisibleFlagService batchEditInvisibleFlagService;

    @Mock
    private SaveProductHelper saveProductHelper;

    @Mock
    private ProductMasterHelper productMasterHelper;

    private Gson gson;
    private ThreadPoolExecutor limitedThreadPoolExecutor;
    private AtomicInteger activeThreadCount;

    @BeforeEach
    void setUp() {
        // Create a limited thread pool with only 4 threads to simulate the deadlock scenario
        limitedThreadPoolExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(4);
        activeThreadCount = new AtomicInteger(0);

        // Initialize Gson instance
        gson = new Gson();
        
        // Manually create the service instance to avoid Spring context issues
        batchEditInvisibleFlagService = new BatchEditInvisibleFlagService(
            limitedThreadPoolExecutor, saveProductHelper, productMasterHelper, gson
        );

        // Set the partition size to ensure we create enough partitions to exhaust the thread pool
        ReflectionTestUtils.setField(batchEditInvisibleFlagService, "updateInvisiblePartitionSize", 2);
    }

    @Test
    void testDeadlockScenario_ThreadPoolExhaustion() {
        // This test demonstrates the deadlock scenario where:
        // 1. start() method uses ecomEngineSyncExecutor 
        // 2. updateInvisible() also uses the same executor
        // 3. All threads get consumed by start() calls, leaving none for updateInvisible()
        
        setupMocksForDeadlockTest();

        // Create multiple requests that will consume all available threads
        List<EditInvisibleRequestDto> requests = createMultipleRequests(6); // More requests than thread pool size

        List<CompletableFuture<Void>> startFutures = new ArrayList<>();

        // Submit multiple start() calls simultaneously to exhaust the thread pool
        for (EditInvisibleRequestDto request : requests) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                // Use the custom service that will create self-referencing async calls
                TestBatchEditInvisibleFlagService testService = new TestBatchEditInvisibleFlagService(
                    limitedThreadPoolExecutor, saveProductHelper, productMasterHelper, gson
                );
                ReflectionTestUtils.setField(testService, "updateInvisiblePartitionSize", 2);
                testService.start(request);
            }, limitedThreadPoolExecutor);
            startFutures.add(future);
        }

        // Monitor thread pool state to detect deadlock
        boolean deadlockDetected = monitorThreadPoolForDeadlock();
        
        if (deadlockDetected) {
            System.out.println("SUCCESS: Deadlock scenario reproduced!");
            System.out.println("Thread Pool State:");
            System.out.println("- Pool Size: " + limitedThreadPoolExecutor.getPoolSize());
            System.out.println("- Active Threads: " + limitedThreadPoolExecutor.getActiveCount());
            System.out.println("- Queue Size: " + limitedThreadPoolExecutor.getQueue().size());
            System.out.println("- Completed Tasks: " + limitedThreadPoolExecutor.getCompletedTaskCount());
            
            // Demonstrate that this is indeed a deadlock by confirming threads are stuck
            assertTrue(limitedThreadPoolExecutor.getActiveCount() > 0, "Should have active threads");
            assertTrue(limitedThreadPoolExecutor.getQueue().size() > 0, "Should have queued tasks");
        }

        // Cancel all futures to clean up
        startFutures.forEach(future -> future.cancel(true));
        limitedThreadPoolExecutor.shutdownNow();
    }

    @Test
    void testNormalOperation_NoDeadlock() {
        // This test verifies normal operation with a larger thread pool
        ThreadPoolExecutor largerThreadPool = (ThreadPoolExecutor) Executors.newFixedThreadPool(20);
        
        batchEditInvisibleFlagService = new BatchEditInvisibleFlagService(
            largerThreadPool, saveProductHelper, productMasterHelper, gson
        );
        ReflectionTestUtils.setField(batchEditInvisibleFlagService, "updateInvisiblePartitionSize", 2);

        setupMocksForNormalTest();

        EditInvisibleRequestDto request = createMultipleRequests(1).get(0);
        
        // This should complete successfully without deadlock
        assertTimeoutPreemptively(Duration.ofSeconds(5), () -> {
            batchEditInvisibleFlagService.start(request);
        }, "Normal operation should complete without deadlock when thread pool is large enough");
        
        largerThreadPool.shutdown();
    }

    private void setupMocksForDeadlockTest() {
        // Mock the product master response to return multiple products that require partitioning
        List<ProductMasterSearchVisibilityResponseDto> mockProducts = createMockProducts(10);
        ProductMasterBaseResponseDto<List<ProductMasterSearchVisibilityResponseDto>> mockResponse = 
                new ProductMasterBaseResponseDto<>();
        mockResponse.setData(mockProducts);
        mockResponse.setStatus(ProductMasterStatusEnum.SUCCESS.name());

        when(productMasterHelper.requestUuidsByStorefrontStoreCodeAndProductIds(any()))
                .thenReturn(mockResponse);

        // Mock the update visibility response with delay to increase thread pool pressure
        ProductMasterBaseResponseDto<List<ProductMasterUpdateVisibilityResponseDto>> updateResponse = 
                new ProductMasterBaseResponseDto<>();
        updateResponse.setData(new ArrayList<>()); // Empty list means no failures
        updateResponse.setStatus(ProductMasterStatusEnum.SUCCESS.name());

        when(productMasterHelper.requestUpdateVisibility(any(ProductMasterUpdateVisibilityRequestDto.class)))
                .thenAnswer(invocation -> {
                    // Simulate processing time to increase chance of thread pool exhaustion
                    activeThreadCount.incrementAndGet();
                    try {
                        Thread.sleep(3000); // Long delay to create thread pool pressure
                        return updateResponse;
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Interrupted during mock processing", e);
                    } finally {
                        activeThreadCount.decrementAndGet();
                    }
                });

        // Mock the saveProductHelper to simulate processing time
        when(saveProductHelper.updateInvisible(anyString(), anyBoolean()))
                .thenAnswer(invocation -> {
                    try {
                        Thread.sleep(100);
                        return null; // null means success
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        return "Interrupted";
                    }
                });
    }

    private boolean monitorThreadPoolForDeadlock() {
        try {
            // Monitor for 8 seconds to detect deadlock
            for (int i = 0; i < 80; i++) {
                Thread.sleep(100);
                
                int poolSize = limitedThreadPoolExecutor.getPoolSize();
                int activeCount = limitedThreadPoolExecutor.getActiveCount();
                int queueSize = limitedThreadPoolExecutor.getQueue().size();
                
                // Deadlock condition: all threads active, tasks in queue, no progress
                if (poolSize == 4 && activeCount == 4 && queueSize > 0) {
                    // Wait a bit more to confirm it's stuck
                    Thread.sleep(2000);
                    
                    int newActiveCount = limitedThreadPoolExecutor.getActiveCount();
                    int newQueueSize = limitedThreadPoolExecutor.getQueue().size();
                    
                    // If state hasn't changed, we likely have a deadlock
                    if (newActiveCount == 4 && newQueueSize >= queueSize) {
                        return true;
                    }
                }
            }
            return false;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    private List<EditInvisibleRequestDto> createMultipleRequests(int count) {
        List<EditInvisibleRequestDto> requests = new ArrayList<>();
        
        for (int i = 0; i < count; i++) {
            EditInvisibleRequestDto request = new EditInvisibleRequestDto();
            request.setInvisible(true);
            
            EditInvisibleProductRequestDto productRequest = new EditInvisibleProductRequestDto();
            productRequest.setStorefrontStoreCode("TEST_STORE_" + i);
            productRequest.setProductCodes(List.of("PRODUCT_" + i + "_1", "PRODUCT_" + i + "_2"));
            
            request.setProducts(List.of(productRequest));
            requests.add(request);
        }
        
        return requests;
    }

    private List<ProductMasterSearchVisibilityResponseDto> createMockProducts(int count) {
        List<ProductMasterSearchVisibilityResponseDto> products = new ArrayList<>();
        
        for (int i = 0; i < count; i++) {
            ProductMasterSearchVisibilityResponseDto product = ProductMasterSearchVisibilityResponseDto.builder()
                    .uuid("uuid-" + i)
                    .storeSkuId("sku-" + i)
                    .build();
            products.add(product);
        }
        
        return products;
    }

    private void setupMocksForNormalTest() {
        List<ProductMasterSearchVisibilityResponseDto> mockProducts = createMockProducts(4);
        ProductMasterBaseResponseDto<List<ProductMasterSearchVisibilityResponseDto>> mockResponse = 
                new ProductMasterBaseResponseDto<>();
        mockResponse.setData(mockProducts);
        mockResponse.setStatus(ProductMasterStatusEnum.SUCCESS.name());

        when(productMasterHelper.requestUuidsByStorefrontStoreCodeAndProductIds(any()))
                .thenReturn(mockResponse);

        ProductMasterBaseResponseDto<List<ProductMasterUpdateVisibilityResponseDto>> updateResponse = 
                new ProductMasterBaseResponseDto<>();
        updateResponse.setData(new ArrayList<>());
        updateResponse.setStatus(ProductMasterStatusEnum.SUCCESS.name());

        when(productMasterHelper.requestUpdateVisibility(any(ProductMasterUpdateVisibilityRequestDto.class)))
                .thenReturn(updateResponse);

        when(saveProductHelper.updateInvisible(anyString(), anyBoolean()))
                .thenReturn(null);
    }

    // Test-specific service class that simulates the self-referencing async behavior
    private static class TestBatchEditInvisibleFlagService extends BatchEditInvisibleFlagService {
        
        public TestBatchEditInvisibleFlagService(java.util.concurrent.Executor executor, 
                SaveProductHelper saveProductHelper, 
                ProductMasterHelper productMasterHelper, 
                Gson gson) {
            super(executor, saveProductHelper, productMasterHelper, gson);
        }

        @Override
        public void start(EditInvisibleRequestDto request) {
            // Override to remove @Async annotation behavior and call the actual method
            // This simulates what happens when Spring's @Async calls the same service
            super.start(request);
        }
    }
}
