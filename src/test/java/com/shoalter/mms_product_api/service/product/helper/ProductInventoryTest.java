package com.shoalter.mms_product_api.service.product.helper;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.shoalter.mms_product_api.helper.HttpRequestHelper;
import com.shoalter.mms_product_api.helper.TokenHelper;
import com.shoalter.mms_product_api.helper.pojo.HttpRequestDto;
import com.shoalter.mms_product_api.service.base.pojo.UserDto;
import com.shoalter.mms_product_api.service.product.pojo.productinventoryapi.EditInventoryRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.productinventoryapi.ProductInventoryResponseDto;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import java.lang.reflect.Type;
import java.util.Date;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
@Disabled
public class ProductInventoryTest {


    private Gson gson;
    private HttpRequestHelper httpRequestHelper;

    private final static String SERVICE_NAME = "Product Inventory";

    private String INVENTORY_API_URL="https://mms-inventory-dev.hkmpcl.com.hk/inventory";
    private String PRODUCT_INVENTORY_SERVICE_NAME="/api/product-inventory";

    @BeforeEach
    public void setup() {
        gson = new Gson();
        httpRequestHelper = new HttpRequestHelper(gson, restTemplate());
    }

    public RestTemplate restTemplate() {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setBufferRequestBody(false);
        return new RestTemplate(requestFactory);
    }

    @Test
    public void Test_Put_ProductInventory() {
        ProductInventoryResponseDto<String> result = httpRequestHelper.requestForBody(HttpRequestDto.<List<EditInventoryRequestDto>, ProductInventoryResponseDto<String>>builder()
                .serviceName(SERVICE_NAME)
                .url(INVENTORY_API_URL + PRODUCT_INVENTORY_SERVICE_NAME)
                .method(HttpMethod.PUT)
                .customHeaders(generateHeaders(getUserDto()))
                .body(getEditRequestDtoList())
                .resultTypeReference(ParameterizedTypeReference.forType(new TypeToken<ProductInventoryResponseDto<String>>() {
                }.getType()))
                .user(getUserDto())
                .build());

        System.out.println("result " + gson.toJson(result));
        assertThat(result.getCode()).isEqualTo("SUCCESS");
    }

    private UserDto getUserDto() {
        return UserDto.builder()
                .userId(369)
                .userName("Steve")
                .userCode("<EMAIL>")
                .email(" <EMAIL>")
                .merchantId(null)
                .roleId(443)
                .roleCode("SUPER_SYSTEM_ADMIN")
                .exp(new Date(System.currentTimeMillis() + 1000000))
                .iat(new Date())
                .build();
    }

    private List<EditInventoryRequestDto> getEditRequestDtoList() {
        Type type = new TypeToken<List<EditInventoryRequestDto>>() {}.getType();
        String editRequestDtoListJson = "[{\"uuid\":\"848874f2-8ec7-4141-bc94-ff0915e8a930\",\"shareStatusData\":{\"nonShareModeDetailDto\":[{\"busUnitCode\":\"HKTV\",\"storeSkuId\":\"CN-IND_STT_SL_1_1\"}]},\"quantityData\":{\"stockQuantityNonShareDetailDto\":[{\"busUnitCode\":\"HKTV\",\"storeSkuId\":\"CN-IND_STT_SL_1_1\",\"action\":\"set\",\"quantity\":0}]},\"stockStatusData\":{\"stockStatusNonShareDetailDto\":[{\"busUnitCode\":\"HKTV\",\"storeSkuId\":\"forceOutOfStock\",\"stockStatus\":\"forceOutOfStock\"}]}},{\"uuid\":\"dabd1cfc-90cc-45ac-a0e5-d17fd312a0a7\",\"shareStatusData\":{\"nonShareModeDetailDto\":[{\"busUnitCode\":\"HKTV\",\"storeSkuId\":\"Adult\"}]},\"quantityData\":{\"stockQuantityNonShareDetailDto\":[{\"busUnitCode\":\"HKTV\",\"storeSkuId\":\"Adult\",\"action\":\"set\",\"quantity\":0}]},\"stockStatusData\":{\"stockStatusNonShareDetailDto\":[{\"busUnitCode\":\"HKTV\",\"storeSkuId\":\"notSpecified\",\"stockStatus\":\"notSpecified\"}]}}]";
        return gson.fromJson(editRequestDtoListJson, type);
    }

    public static class FakeTokenHelper extends TokenHelper {
        @Override
        public String generateToken(UserDto userDto) {
            String JWT_SECURITY_KEY = "e905a8d92392f5104ce13783af3af6a167c5bee3402f18e6cc7f145b44e1bf24a7458cc4de8bee6f24089e94ce2cc7c914d02de9c409f0de9f1f28f1a9a67ac91268b44a4cc5573451fb7908e1a675ef22971a97e382b6fe791f1d7e36df27fd50c1df607f481ba4cdb494cde87e2ff86788d4423263c565bae6d5c8692e2289159c26f4c33b357e1381989dcf2f2a70de22114244166aa9ad58502ddbb65ea94baf800c52c23502d581b3aa7d847d2950eaa337270b2ef385780ab9969ae2da0d763b2f92726043ec9df164c237d650927d470fef0ed5ad00d1edba19a5f311c3585598fb15e747389fc40c28df05c3060078fac7514137a5b87492762edcdc";
            return Jwts
                    .builder()
                    // 主题
                    .setSubject("mms")
                    .claim("typ", "Bearer")
                    // 添加jwt自定义值
                    .claim("userId", userDto.getUserId())
                    .claim("username", userDto.getUserName())
                    .claim("userCode", userDto.getUserCode())
                    .claim("email", userDto.getEmail())
                    .claim("merchantId", userDto.getMerchantId())
                    .claim("ssoUserId", userDto.getSsoUserId())
                    .claim("roleId", userDto.getRoleId())
                    .claim("roleCode", userDto.getRoleCode())
                    .claim("roleName", userDto.getRoleName())
                    .claim("remark", userDto.getRemark())
                    .claim("roleType", userDto.getRoleType())
                    .claim("system_code", "mms")
                    .setIssuedAt(userDto.getIat())
                    // 過期時間
                    .setExpiration(userDto.getExp())
                    // 加密方式 加密key
                    .signWith(SignatureAlgorithm.HS256, JWT_SECURITY_KEY).compact();
        }
    }

    private HttpHeaders generateHeaders(UserDto user) {
        TokenHelper tokenHelper = new FakeTokenHelper();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(tokenHelper.generateToken(user));
        headers.set("user-agent","mms");
        return headers;
    }
}
