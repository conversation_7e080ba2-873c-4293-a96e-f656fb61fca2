package com.shoalter.mms_product_api.service.product;

import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import org.apache.commons.collections4.ListUtils;
import org.junit.jupiter.api.Test;

/**
 * This test demonstrates the deadlock issue in BatchEditInvisibleFlagService.
 * 
 * The problem occurs because:
 * 1. start() method is @Async("ecomEngineSyncExecutor")
 * 2. updateInvisible() method is also @Async("ecomEngineSyncExecutor")
 * 3. start() calls updateInvisible() creating nested async dependencies on the same thread pool
 * 4. When the thread pool is exhausted by start() calls, updateInvisible() calls cannot execute
 * 5. This creates a deadlock where all threads are waiting for nested async calls that will never execute
 */
class BatchEditInvisibleFlagServiceDeadlockReproductionTest {

    @Test
    void reproduceDeadlockScenario() {
        System.out.println("=== DEADLOCK REPRODUCTION TEST ===");
        
        // Create a limited thread pool (same concept as ecomEngineSyncExecutor)
        ThreadPoolExecutor limitedExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(3);
        
        System.out.println("Thread pool size: " + limitedExecutor.getMaximumPoolSize());
        
        AtomicInteger tasksStarted = new AtomicInteger(0);
        AtomicInteger tasksCompleted = new AtomicInteger(0);
        
        List<CompletableFuture<String>> outerTasks = new ArrayList<>();
        
        // Submit multiple "start()" equivalent tasks
        for (int i = 0; i < 6; i++) { // More tasks than thread pool size
            final int taskId = i;
            
            CompletableFuture<String> outerTask = CompletableFuture.supplyAsync(() -> {
                tasksStarted.incrementAndGet();
                System.out.println("OUTER Task " + taskId + " started on thread: " + Thread.currentThread().getName());
                
                try {
                    // Simulate work in start() method before calling updateInvisible()
                    Thread.sleep(500);
                    
                    // Simulate partitioning logic that creates multiple updateInvisible() calls
                    List<String> data = List.of("item1", "item2", "item3", "item4", "item5", "item6");
                    List<List<String>> partitions = ListUtils.partition(data, 2); // Creates 3 partitions
                    
                    System.out.println("OUTER Task " + taskId + ": Created " + partitions.size() + " partitions");
                    
                    List<CompletableFuture<String>> innerTasks = new ArrayList<>();
                      // This is where the deadlock occurs: submitting to the same thread pool
                    for (int p = 0; p < partitions.size(); p++) {
                        final int partitionId = p;
                        
                        System.out.println("OUTER Task " + taskId + ": Submitting INNER task " + partitionId + 
                                         " to the SAME thread pool (this causes deadlock)");
                        
                        CompletableFuture<String> innerTask = CompletableFuture.supplyAsync(() -> {
                            System.out.println("INNER Task " + taskId + "-" + partitionId + 
                                             " started on thread: " + Thread.currentThread().getName());
                            try {
                                // Simulate updateInvisible() processing
                                Thread.sleep(1000);
                                System.out.println("INNER Task " + taskId + "-" + partitionId + " completed");
                                return "INNER-SUCCESS";
                            } catch (Exception e) {
                                System.out.println("INNER Task " + taskId + "-" + partitionId + " failed: " + e.getMessage());
                                return "INNER-FAILED";
                            }
                        }, limitedExecutor); // SAME EXECUTOR - this is the problem!
                        
                        innerTasks.add(innerTask);
                    }
                    
                    System.out.println("OUTER Task " + taskId + ": Waiting for all INNER tasks to complete...");
                    
                    // This join() will block forever when thread pool is exhausted
                    CompletableFuture.allOf(innerTasks.toArray(new CompletableFuture[0])).join();
                    
                    tasksCompleted.incrementAndGet();
                    System.out.println("OUTER Task " + taskId + ": All INNER tasks completed - SUCCESS!");
                    return "OUTER-SUCCESS";
                    
                } catch (Exception e) {
                    System.out.println("OUTER Task " + taskId + ": Failed with exception: " + e.getMessage());
                    return "OUTER-FAILED";
                }
            }, limitedExecutor);
            
            outerTasks.add(outerTask);
        }
        
        // Monitor the deadlock for 10 seconds
        try {
            System.out.println("\n=== MONITORING THREAD POOL FOR 10 SECONDS ===");
            
            for (int i = 0; i < 100; i++) { // Monitor for 10 seconds
                Thread.sleep(100);
                
                if (i % 20 == 0) { // Print status every 2 seconds
                    System.out.println("Time: " + (i/10) + "s | " +
                                     "Pool size: " + limitedExecutor.getPoolSize() + " | " +
                                     "Active: " + limitedExecutor.getActiveCount() + " | " +
                                     "Queue: " + limitedExecutor.getQueue().size() + " | " +
                                     "Started: " + tasksStarted.get() + " | " +
                                     "Completed: " + tasksCompleted.get());
                }
                
                // Check for deadlock condition
                if (limitedExecutor.getActiveCount() == limitedExecutor.getMaximumPoolSize() && 
                    limitedExecutor.getQueue().size() > 0 && 
                    tasksCompleted.get() == 0) {
                    
                    System.out.println("\n🔒 DEADLOCK DETECTED! 🔒");
                    System.out.println("- All threads (" + limitedExecutor.getActiveCount() + ") are active and blocked");
                    System.out.println("- " + limitedExecutor.getQueue().size() + " tasks are queued waiting for threads");
                    System.out.println("- No tasks have completed (" + tasksCompleted.get() + " completed)");
                    System.out.println("- OUTER tasks are waiting for INNER tasks");
                    System.out.println("- INNER tasks cannot start because no threads are available");
                    System.out.println("- This is exactly what happens in BatchEditInvisibleFlagService!");
                    break;
                }
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        System.out.println("\n=== FINAL STATE ===");
        System.out.println("Tasks started: " + tasksStarted.get());
        System.out.println("Tasks completed: " + tasksCompleted.get());
        System.out.println("Active threads: " + limitedExecutor.getActiveCount());
        System.out.println("Queued tasks: " + limitedExecutor.getQueue().size());
        
        // Verify deadlock occurred
        assertTrue(limitedExecutor.getActiveCount() > 0, 
                   "Should have active threads blocked in deadlock");
        assertTrue(limitedExecutor.getQueue().size() > 0, 
                   "Should have queued tasks waiting for threads");
        assertTrue(tasksCompleted.get() == 0, 
                   "No tasks should complete due to deadlock");
        
        System.out.println("\n✅ DEADLOCK SUCCESSFULLY REPRODUCED!");
        System.out.println("\nSOLUTION: Use different thread pools for start() and updateInvisible() methods");
        System.out.println("or restructure to avoid nested async dependencies on the same executor.");
        
        // Clean up
        outerTasks.forEach(f -> f.cancel(true));
        limitedExecutor.shutdownNow();
    }
}
