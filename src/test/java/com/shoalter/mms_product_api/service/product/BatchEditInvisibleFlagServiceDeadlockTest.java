package com.shoalter.mms_product_api.service.product;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;

import com.google.gson.Gson;
import com.shoalter.mms_product_api.config.product.ProductMasterStatusEnum;
import com.shoalter.mms_product_api.service.product.helper.ProductMasterHelper;
import com.shoalter.mms_product_api.service.product.helper.SaveProductHelper;
import com.shoalter.mms_product_api.service.product.pojo.EditInvisibleProductRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.EditInvisibleRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.ProductMasterBaseResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.request.ProductMasterUpdateVisibilityRequestDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterSearchVisibilityResponseDto;
import com.shoalter.mms_product_api.service.product.pojo.productmaster.response.ProductMasterUpdateVisibilityResponseDto;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BatchEditInvisibleFlagServiceDeadlockTest {    @Mock
    private SaveProductHelper saveProductHelper;

    @Mock
    private ProductMasterHelper productMasterHelper;

    private ThreadPoolExecutor limitedThreadPoolExecutor;    @BeforeEach
    void setUp() {
        // Create a small thread pool to easily trigger deadlock
        limitedThreadPoolExecutor = (ThreadPoolExecutor) Executors.newFixedThreadPool(2);
        
        setupMocks();
    }

    @Test
    void demonstrateDeadlockIssue() {
        /*
         * DEADLOCK REPRODUCTION TEST
         * 
         * This test reproduces the deadlock issue in BatchEditInvisibleFlagService where:
         * 
         * 1. The start() method is annotated with @Async("ecomEngineSyncExecutor")
         * 2. Inside start(), it calls updateInvisible() which is also @Async("ecomEngineSyncExecutor") 
         * 3. When the thread pool is exhausted by start() calls, updateInvisible() calls cannot execute
         * 4. This creates a deadlock where start() waits for updateInvisible() but no threads are available
         * 
         * The root cause is that both methods use the same executor, creating a nested async dependency.
         */
        
        System.out.println("=== DEADLOCK REPRODUCTION TEST ===");
        System.out.println("Thread pool size: " + limitedThreadPoolExecutor.getMaximumPoolSize());
        
        // Create a service that manually simulates the async behavior
        DeadlockSimulationService simulationService = new DeadlockSimulationService();
        
        // Create test data with enough products to trigger partitioning
        EditInvisibleRequestDto request = createTestRequest();
        
        // Submit multiple tasks to exhaust the thread pool
        AtomicInteger completedTasks = new AtomicInteger(0);
        AtomicInteger deadlockedTasks = new AtomicInteger(0);
        
        List<CompletableFuture<String>> futures = new ArrayList<>();
        
        // Submit 4 tasks (double the thread pool size) to guarantee exhaustion
        for (int i = 0; i < 4; i++) {
            final int taskId = i;
            CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                try {
                    System.out.println("Task " + taskId + " started on thread: " + Thread.currentThread().getName());
                    String result = simulationService.simulateStart(request, taskId);
                    completedTasks.incrementAndGet();
                    System.out.println("Task " + taskId + " completed successfully");
                    return result;
                } catch (Exception e) {
                    deadlockedTasks.incrementAndGet();
                    System.out.println("Task " + taskId + " failed/deadlocked: " + e.getMessage());
                    return "DEADLOCKED";
                }
            }, limitedThreadPoolExecutor);
            
            futures.add(future);
        }
        
        // Wait for a reasonable time and check the state
        try {
            Thread.sleep(8000); // Wait 8 seconds
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // Check thread pool state
        System.out.println("\n=== THREAD POOL STATE AFTER 8 SECONDS ===");
        System.out.println("Active threads: " + limitedThreadPoolExecutor.getActiveCount());
        System.out.println("Queue size: " + limitedThreadPoolExecutor.getQueue().size());
        System.out.println("Completed tasks: " + completedTasks.get());
        System.out.println("Pool size: " + limitedThreadPoolExecutor.getPoolSize());
        System.out.println("Completed task count: " + limitedThreadPoolExecutor.getCompletedTaskCount());
        
        // Cancel all remaining futures
        futures.forEach(f -> f.cancel(true));
        
        // The test should show that:
        // 1. All thread pool threads are active (blocked)
        // 2. There are tasks queued waiting for threads
        // 3. No tasks complete because they're all waiting for nested async calls
        
        assertTrue(limitedThreadPoolExecutor.getActiveCount() > 0, 
                   "Should have active threads (blocked waiting for nested async calls)");
        
        assertTrue(limitedThreadPoolExecutor.getQueue().size() > 0, 
                   "Should have queued tasks waiting for available threads");
        
        System.out.println("\n=== DEADLOCK SUCCESSFULLY DEMONSTRATED ===");
        System.out.println("This shows that the BatchEditInvisibleFlagService has a deadlock issue:");
        System.out.println("- start() method blocks threads waiting for updateInvisible() calls");
        System.out.println("- updateInvisible() calls cannot execute because no threads are available");
        System.out.println("- All threads are deadlocked waiting for each other");
        
        limitedThreadPoolExecutor.shutdownNow();
    }

    private void setupMocks() {        // Create mock products that will cause partitioning
        List<ProductMasterSearchVisibilityResponseDto> mockProducts = new ArrayList<>();
        for (int i = 0; i < 8; i++) {
            mockProducts.add(ProductMasterSearchVisibilityResponseDto.builder()
                    .uuid("uuid-" + i)
                    .storeSkuId("sku-" + i)
                    .build());
        }

        ProductMasterBaseResponseDto<List<ProductMasterSearchVisibilityResponseDto>> searchResponse = 
                new ProductMasterBaseResponseDto<>();
        searchResponse.setData(mockProducts);
        searchResponse.setStatus(ProductMasterStatusEnum.SUCCESS.name());

        lenient().when(productMasterHelper.requestUuidsByStorefrontStoreCodeAndProductIds(any()))
                .thenReturn(searchResponse);

        ProductMasterBaseResponseDto<List<ProductMasterUpdateVisibilityResponseDto>> updateResponse = 
                new ProductMasterBaseResponseDto<>();
        updateResponse.setData(new ArrayList<>());
        updateResponse.setStatus(ProductMasterStatusEnum.SUCCESS.name());

        lenient().when(productMasterHelper.requestUpdateVisibility(any(ProductMasterUpdateVisibilityRequestDto.class)))
                .thenAnswer(invocation -> {
                    // Simulate processing time that makes deadlock more likely
                    Thread.sleep(2000);
                    return updateResponse;
                });

        lenient().when(saveProductHelper.updateInvisible(any(), any())).thenReturn(null);
    }

    private EditInvisibleRequestDto createTestRequest() {
        EditInvisibleRequestDto request = new EditInvisibleRequestDto();
        request.setInvisible(true);
        
        EditInvisibleProductRequestDto productRequest = new EditInvisibleProductRequestDto();
        productRequest.setStorefrontStoreCode("TEST_STORE");
        productRequest.setProductCodes(List.of("PRODUCT_1", "PRODUCT_2", "PRODUCT_3", "PRODUCT_4"));
        
        request.setProducts(List.of(productRequest));
        return request;
    }

    /**
     * This class simulates the deadlock behavior of BatchEditInvisibleFlagService
     * by manually reproducing the async call pattern that causes the issue.
     */
    private class DeadlockSimulationService {
        
        public String simulateStart(EditInvisibleRequestDto request, int taskId) {
            try {
                System.out.println("Task " + taskId + ": Getting product UUIDs...");
                
                // Simulate the product master call
                ProductMasterBaseResponseDto<List<ProductMasterSearchVisibilityResponseDto>> productMasterResponse =
                        productMasterHelper.requestUuidsByStorefrontStoreCodeAndProductIds(request.getProducts());

                if (productMasterResponse == null || productMasterResponse.getData() == null) {
                    return "ERROR: No product data";
                }

                List<ProductMasterSearchVisibilityResponseDto> responseData = productMasterResponse.getData();
                
                // Partition the data (simulating the same logic as the real service)
                List<List<ProductMasterSearchVisibilityResponseDto>> partitions = 
                        ListUtils.partition(responseData, 2); // Small partition size to create more async calls

                System.out.println("Task " + taskId + ": Created " + partitions.size() + " partitions");

                List<CompletableFuture<String>> futures = new ArrayList<>();

                // This is where the deadlock occurs: we submit async tasks to the same thread pool
                // that is already executing this method, creating a circular dependency
                for (int i = 0; i < partitions.size(); i++) {
                    final int partitionIndex = i;
                    final List<ProductMasterSearchVisibilityResponseDto> partition = partitions.get(i);
                    
                    System.out.println("Task " + taskId + ": Submitting partition " + partitionIndex + 
                                     " to the same thread pool...");
                    
                    // This is the problematic line: submitting to the same executor that's running this method
                    CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                        return simulateUpdateInvisible(partition, request.getInvisible(), 
                                                     Pair.of(partitionIndex + 1, partitions.size()), taskId);
                    }, limitedThreadPoolExecutor);
                    
                    futures.add(future);
                }

                System.out.println("Task " + taskId + ": Waiting for " + futures.size() + " partition updates...");
                
                // This join() will block forever if the thread pool is exhausted
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                
                System.out.println("Task " + taskId + ": All partitions completed successfully");
                return "SUCCESS";
                
            } catch (Exception e) {
                System.out.println("Task " + taskId + ": Exception occurred - " + e.getMessage());
                throw new RuntimeException("Task " + taskId + " failed", e);
            }
        }

        private String simulateUpdateInvisible(List<ProductMasterSearchVisibilityResponseDto> responseDtoList, 
                                             boolean invisible, 
                                             Pair<Integer, Integer> partitionIndexPair,
                                             int taskId) {
            try {
                System.out.println("Task " + taskId + ", Partition " + partitionIndexPair.getLeft() + 
                                 ": Starting update on thread " + Thread.currentThread().getName());                // Simulate the product master update call
                List<String> uuids = responseDtoList.stream()
                        .map(ProductMasterSearchVisibilityResponseDto::getUuid)
                        .collect(Collectors.toList());

                ProductMasterUpdateVisibilityRequestDto updateRequest = 
                        ProductMasterUpdateVisibilityRequestDto.builder()
                                .visibility(invisible ? "N" : "Y")
                                .uuids(uuids)
                                .build();

                productMasterHelper.requestUpdateVisibility(updateRequest);

                System.out.println("Task " + taskId + ", Partition " + partitionIndexPair.getLeft() + 
                                 ": Update completed successfully");
                return "SUCCESS";
                
            } catch (Exception e) {
                System.out.println("Task " + taskId + ", Partition " + partitionIndexPair.getLeft() + 
                                 ": Update failed - " + e.getMessage());
                throw new RuntimeException("Partition update failed", e);
            }
        }
    }
}
