# Edit Product 完整流程技術文件

## 概述

本文件詳細說明產品編輯的完整流程，從建立記錄開始，到同步給 ProductMaster (PM)，再到接收 PM 的 Message Queue 回應，最後更新相關數據庫資料並同步給 Hybris 的整個過程。

## 整體架構圖

```
用戶請求
    ↓
SingleEditProductService.start()
    ↓
建立 SaveProductRecord
    ↓
同步給 Product Master
    ↓
ProductMaster 處理並發送 MQ
    ↓
ProductMasterSingleProductHandler 接收 MQ
    ↓
SaveProductHelper 處理
    ↓
更新本地數據庫 & 同步給 Hybris
    ↓
回應 ProductMaster
```

## 完整流程詳細說明

### 第一階段：建立記錄 (SingleEditProductService)

這部分在前面的文件中已詳細說明，主要包括：

1. **數據驗證** - 權限檢查、業務規則驗證
2. **記錄創建** - 創建 `SaveProductRecordDo` 和 `SaveProductRecordRowDo`
3. **狀態設置** - 初始狀態為 `WAIT_START`，處理後為 `REQUESTING_PM`

### 第二階段：同步給 ProductMaster

#### 2.1 callProductMasterAndUpdateRecordRow()

```java
@Transactional
public void callProductMasterAndUpdateRecordRow(Long recordId) {
    SaveProductRecordDo saveProductRecordDo = saveProductRecordRepository.findById(recordId).orElseThrow();
    List<SaveProductRecordRowDo> rows = saveProductRecordRowRepository.findByRecordIdAndStatus(recordId, SaveProductStatus.REQUESTING_PM);
    
    if (rows.isEmpty()) {
        saveProductRecordDo.setStatus(SaveProductStatus.FAIL);
        return;
    }

    ProductMasterResponseDto productMasterResponseDto = checkRequestPMRecordProductTask.requestSendProductToProductMaster(saveProductRecordDo, rows);
    saveProductRecordHelper.updateRecordByProductMasterResult(productMasterResponseDto, saveProductRecordDo, rows);
}
```

**目的**：
- 查詢狀態為 `REQUESTING_PM` 的記錄行
- 調用 `CheckRequestPMRecordProductTask` 發送數據到 ProductMaster
- 根據回應結果更新記錄狀態

#### 2.2 CheckRequestPMRecordProductTask 處理

`CheckRequestPMRecordProductTask` 負責：

1. **數據準備**：將記錄行轉換為 ProductMaster 所需格式
2. **批次處理**：按變體產品分組，控制每批次大小
3. **API 調用**：發送 HTTP 請求到 ProductMaster
4. **狀態更新**：
   - 成功：記錄行狀態改為 `CHECKING_PM`
   - 失敗：記錄行狀態改為 `FAIL`

**關鍵邏輯**：
```java
private void handleRequestingProductMasterFlow(SaveProductRecordDo record, List<SaveProductRecordRowDo> waitSendPmList) {
    List<List<SaveProductRecordRowDo>> partitionList = splitRecordRowByVariantAndSize(record, waitSendPmList, productProcessSizeLimit);
    
    for (List<SaveProductRecordRowDo> splitWaitSendPmListSplit : partitionList) {
        ProductMasterResponseDto responseDto = requestSendProductToProductMaster(record, splitWaitSendPmListSplit);
        if (CollectionUtil.isEmpty(responseDto.getErrorMessageList())) {
            pmTractIds.add(responseDto.getTraceId());
            splitWaitSendPmListSplit.forEach(row -> {
                row.setStatus(SaveProductStatus.CHECKING_PM);
            });
            successCount.getAndAdd(splitWaitSendPmListSplit.size());
        }
    }
}
```

### 第三階段：ProductMaster 處理與 Message Queue

#### 3.1 ProductMaster 內部處理

ProductMaster 接收到請求後：

1. **數據驗證** - 檢查產品數據有效性
2. **業務邏輯處理** - 執行產品編輯邏輯
3. **數據持久化** - 更新 ProductMaster 數據庫
4. **MQ 發送** - 發送處理結果到 MQ

#### 3.2 Message Queue 結構

ProductMaster 發送的 MQ 消息結構：

```java
public class ProductMasterMqMessageDto {
    private String action;        // CREATE, UPDATE, OFFLINE
    private String userCode;      // 用戶代碼
    private List<ProductMasterMqDto> products;  // 產品列表
}

public class ProductMasterMqDto {
    private String uuid;          // 產品 UUID
    private String skuCode;       // SKU 代碼
    private String stores;        // 店鋪信息
    private String productId;     // 產品 ID
    private Long recordRowId;     // 記錄行 ID
    private Integer version;      // 版本號
    // ... 其他產品屬性
}
```

### 第四階段：Message Queue 處理 (ProductMasterSingleProductHandler)

#### 4.1 MQ 接收與處理

```java
@RabbitListener(queues = "${mpps.rabbitmq.single.queue.name}", containerFactory = "productMasterSingleContainerFactory", ackMode = "MANUAL")
public void receive(@Payload ProductMasterMqMessageDto productMasterMqMessageDto, Message message, Channel channel) {
    try {
        long startTime = System.currentTimeMillis();
        String correlationId = message.getMessageProperties().getCorrelationId();
        
        UserDto userDto = getUserDto(productMasterMqMessageDto.getUserCode());
        switch (productMasterMqMessageDto.getAction()) {
            case "CREATE":
            case "UPDATE":
                saveProduct(userDto, productMasterMqMessageDto, correlationId);
                break;
            case "OFFLINE":
                saveProductOffline(userDto, productMasterMqMessageDto, correlationId);
                break;
            default:
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                return;
        }
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    } catch (Exception e) {
        channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        log.error("Consumer mms_product_single-product-info-hktvmall_queue fail", e);
    }
}
```

**關鍵特點**：
- **手動 ACK**：確保消息處理完成後才確認
- **異常處理**：失敗時仍然 ACK，避免消息重複處理
- **分類處理**：根據 action 類型執行不同邏輯

#### 4.2 saveProduct() 處理邏輯

```java
private void saveProduct(UserDto userDto, ProductMasterMqMessageDto productMasterMqMessageDto, String correlationId) {
    // 按店鋪和產品 ID 分組（變體 SKU）
    Map<String, List<ProductMasterMqDto>> variantSkuMap = productMasterMqMessageDto.getProducts().stream()
        .collect(Collectors.groupingBy(data -> data.getStores() + StringUtil.UNDERLINE + data.getProductId()));

    // 異步並行處理每組產品
    List<ProductMasterReturnDto> productMasterReturnDto = variantSkuMap.entrySet().stream()
        .map((entry) -> CompletableFuture.supplyAsync(() -> {
            log.info("saveProduct with multithread to hybris store product {}", entry.getKey());
            return saveProductHelper.createOrUpdateProducts(userDto, entry.getValue());
        }, productSyncExecutor))
        .map(CompletableFuture::join)
        .collect(Collectors.toList())
        .stream()
        .flatMap(Collection::stream)
        .collect(Collectors.toList());

    // 回傳處理結果給 ProductMaster
    if (CollectionUtil.isNotEmpty(productMasterReturnDto)) {
        rabbitTemplateHelper.sendProductResultToProductMaster(productMasterReturnDto, correlationId);
    }
}
```

**設計特點**：
- **分組處理**：按變體 SKU 分組，確保相關產品一起處理
- **並行處理**：使用 `CompletableFuture` 並行處理不同組別
- **結果回饋**：處理完成後發送結果回 ProductMaster

#### 4.3 saveProductOffline() 處理邏輯

```java
private void saveProductOffline(UserDto userDto, ProductMasterMqMessageDto productMasterMqMessageDto, String correlationId) {
    List<ProductMasterReturnDto> productMasterReturnDto = productMasterMqMessageDto.getProducts().stream().parallel()
        .map(product -> CompletableFuture.supplyAsync(() -> {
            log.info("saveProduct with multithread to hybris uuid: {}, version: {}, record row id: {}", 
                    product.getUuid(), product.getVersion(), product.getRecordRowId());
            return saveProductHelper.updateProductOffline(userDto, product);
        }, productSyncExecutor))
        .map(CompletableFuture::join)
        .collect(Collectors.toList());
    
    // 回傳處理結果給 ProductMaster  
    if (CollectionUtil.isNotEmpty(productMasterReturnDto)) {
        rabbitTemplateHelper.sendProductResultToProductMaster(productMasterReturnDto, correlationId);
    }
}
```

### 第五階段：數據庫更新與 Hybris 同步 (SaveProductHelper)

#### 5.1 SaveProductHelper.createOrUpdateProducts()

```java
public List<ProductMasterReturnDto> createOrUpdateProducts(UserDto userDto, List<ProductMasterMqDto> productMasterMqDto) {
    List<ProductMasterReturnDto> results = new ArrayList<>();
    Set<Long> failedRecordRowId = new HashSet<>();
    
    for (ProductMasterMqDto product : productMasterMqDto) {
        // 查詢相關的記錄
        SaveProductRecordDo saveProductRecordDo = saveProductRecordRepository.findByRecordRowId(product.getRecordRowId());
        
        // 執行創建或更新邏輯
        ProductMasterReturnDto result = SpringBeanProvider.getBean(SaveProductHelper.class)
            .createOrUpdateProduct(userDto, product, saveProductRecordDo, saveProductData);
        results.add(result);
        
        if (StatusCodeEnum.FAIL.name().equals(result.getResult())) {
            failedRecordRowId.add(product.getRecordRowId());
        }
    }
    
    return results;
}
```

#### 5.2 SaveProductHelper.createOrUpdateProduct()

這是核心的產品處理邏輯：

```java
@Transactional
public ProductMasterReturnDto createOrUpdateProduct(UserDto userDto, ProductMasterMqDto productMasterMqDto, 
                                                   SaveProductRecordDo saveProductRecordDo, SaveProductData saveProductData) {
    ProductMasterReturnDto result;
    
    // 區分一般產品和組合產品
    if (productMasterMqDto.getBundleSetting() == null) {
        // 一般產品處理
        Optional<ProductDo> productDoOpt = productRepository.findByUuid(productMasterMqDto.getUuid());
        if (productDoOpt.isEmpty()) {
            result = createProduct(userDto, productMasterMqDto, saveProductRecordDo, saveProductData);
        } else {
            result = updateProduct(userDto, productMasterMqDto, productDoOpt.get(), saveProductRecordDo, saveProductData);
        }
    } else {
        // 組合產品處理
        Optional<BundleProductDo> bundleProductDoOpt = bundleProductRepository.findByUuid(productMasterMqDto.getUuid());
        if (bundleProductDoOpt.isEmpty()) {
            result = createBundle(userDto, productMasterMqDto, saveProductRecordDo, saveProductData);
        } else {
            result = updateBundle(userDto, productMasterMqDto, bundleProductDoOpt.get(), saveProductRecordDo, saveProductData);
        }
    }
    
    return result;
}
```

#### 5.3 Hybris 同步處理

在產品更新過程中，系統會同步數據到 Hybris：

```java
private void updateProductAndSaveHistory(UserDto userDto, SaveProductRecordDo saveProductRecordDo, ProductMasterMqDto productMasterMqDto) {
    String productUuid = productMasterMqDto.getUuid();
    
    // 更新產品詳細信息
    ProductDo productDo = updateProductDetails(userDto, saveProductRecordDo, productMasterMqDto, productUuid);
    
    // 更新產品店鋪狀態
    updateProductStoreStatusDetails(saveProductRecordDo, productMasterMqDto, productUuid);
    
    // 保存歷史記錄
    ProductStoreStatusHistoryDo productStoreStatusHistoryDo = saveProductHelper.generateProductStoreStatusHistory(
        productDo, productMasterMqDto);
    saveProductHelper.saveProductHistory(productDo.getId(), saveProductRecordDo.getUserIp(), productStoreStatusHistoryDo);
}
```

**Hybris 同步的類型**：
- **價格更新** (`PRODUCT_SYNC_MODE_PRICE`)
- **上下線狀態** (`PRODUCT_SYNC_MODE_ONOFFLINE`)
- **可見性** (`PRODUCT_SYNC_MODE_VISIBILITY`)
- **Mall Dollar** (`PRODUCT_SYNC_MODE_MALL_DOLLAR`)
- **準備天數** (`PRODUCT_SYNC_MODE_PICK_UP_DAYS`)

### 第六階段：回應 ProductMaster

#### 6.1 處理結果回饋

```java
// 在 ProductMasterSingleProductHandler 中
if (CollectionUtil.isNotEmpty(productMasterReturnDto)) {
    rabbitTemplateHelper.sendProductResultToProductMaster(productMasterReturnDto, correlationId);
}
```

#### 6.2 ProductMasterReturnDto 結構

```java
public class ProductMasterReturnDto {
    private String uuid;           // 產品 UUID
    private String result;         // SUCCESS/FAIL
    private List<String> failedReason;  // 失敗原因
    private Long recordRowId;      // 記錄行 ID
    private String skuCode;        // SKU 代碼
    private Boolean forceRollback; // 是否強制回滾
}
```

## 狀態流轉圖

```
產品編輯請求
    ↓
WAIT_START (等待開始)
    ↓
PROCESSING (處理中)
    ↓
REQUESTING_PM (請求 ProductMaster)
    ↓
CHECKING_PM (ProductMaster 處理中)
    ↓
[ProductMaster MQ 處理]
    ↓
SUCCESS/FAIL (最終狀態)
```

## 錯誤處理機制

### 1. 階段性錯誤處理

- **第一階段錯誤**：數據驗證失敗，直接返回錯誤
- **第二階段錯誤**：PM 請求失敗，記錄狀態設為 `FAIL`
- **第三階段錯誤**：MQ 處理失敗，通過 ACK 機制避免重複處理
- **第四階段錯誤**：數據庫或 Hybris 同步失敗，返回失敗結果給 PM

### 2. 事務管理

- **本地事務**：每個階段都有獨立的事務管理
- **分布式事務**：通過狀態機制和補償邏輯實現最終一致性
- **回滾機制**：失敗時可以通過 `forceRollback` 標記進行補償

### 3. 重試機制

- **MQ 重試**：通過 RabbitMQ 的重試機制
- **API 重試**：在 HTTP 客戶端層實現重試
- **數據補償**：通過定時任務檢查和修復不一致狀態

## 性能優化特點

### 1. 異步處理

- **並行處理**：MQ 消息並行處理多個產品
- **線程池**：使用 `productSyncExecutor` 線程池
- **批次處理**：按變體 SKU 分組批次處理

### 2. 數據庫優化

- **批量操作**：使用 `saveAll()` 進行批量保存
- **索引利用**：通過 UUID 等索引快速查詢
- **連接池**：數據庫連接池管理

### 3. 緩存策略

- **用戶信息緩存**：避免重複查詢用戶資料
- **產品數據緩存**：減少數據庫查詢次數

## 監控與日誌

### 1. 關鍵日誌點

```java
// MQ 接收日誌
log.info("Consumer mms_product_single-product-info-hktvmall_queue, correlationId: {}", correlationId);

// 處理時間監控
log.info("Time taken by ProductMasterSingleProductHandler receive method : {} milliseconds", endTime - startTime);

// 處理結果日誌
log.info("saveProduct with multithread to hybris uuid: {}, version: {}, record row id: {}", 
         product.getUuid(), product.getVersion(), product.getRecordRowId());
```

### 2. 性能監控

- **處理時間追踪**：記錄每個階段的處理時間
- **成功率監控**：統計成功和失敗的比例
- **資源使用監控**：監控線程池和數據庫連接使用情況

## 配置參數

### 1. MQ 配置

```properties
mpps.rabbitmq.single.queue.name=mms_product_single-product-info-hktvmall_queue
```

### 2. 性能配置

```properties
product.process.size.limit=100  # 每批次處理數量限制
```

### 3. 線程池配置

```java
@Bean("productSyncExecutor")
public Executor productSyncExecutor() {
    // 線程池配置
}
```

## 總結

整個 edit product 流程涵蓋了從用戶請求到最終數據同步的完整鏈路：

1. **用戶請求處理** - 驗證和記錄創建
2. **ProductMaster 同步** - 發送數據到中央產品管理系統
3. **Message Queue 處理** - 異步處理和狀態更新
4. **數據庫同步** - 更新本地數據和歷史記錄
5. **Hybris 同步** - 同步到電商前台系統
6. **結果回饋** - 將處理結果回饋給 ProductMaster

這個架構確保了：
- **數據一致性** - 通過狀態機制和事務管理
- **高性能** - 通過異步處理和並行操作
- **高可靠性** - 通過錯誤處理和重試機制
- **可觀測性** - 通過完整的日誌和監控

整個流程設計充分考慮了微服務架構下的分布式系統特點，實現了高效、可靠的產品編輯功能。
